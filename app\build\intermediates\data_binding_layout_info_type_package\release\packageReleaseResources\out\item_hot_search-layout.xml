<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_hot_search" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\item_hot_search.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/item_hot_search_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="51" endOffset="51"/></Target><Target id="@+id/rankTextView" view="TextView"><Expressions/><location startLine="13" startOffset="4" endLine="25" endOffset="24"/></Target><Target id="@+id/searchWordTextView" view="TextView"><Expressions/><location startLine="27" startOffset="4" endLine="39" endOffset="26"/></Target><Target id="@+id/scoreTextView" view="TextView"><Expressions/><location startLine="41" startOffset="4" endLine="50" endOffset="27"/></Target></Targets></Layout>