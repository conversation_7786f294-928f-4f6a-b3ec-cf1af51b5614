<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_user_profile" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\fragment_user_profile.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/fragment_user_profile_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="472" endOffset="53"/></Target><Target id="@+id/appbar_layout" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="8" startOffset="4" endLine="281" endOffset="53"/></Target><Target id="@+id/collapsing_toolbar" view="com.google.android.material.appbar.CollapsingToolbarLayout"><Expressions/><location startLine="15" startOffset="8" endLine="280" endOffset="68"/></Target><Target id="@+id/iv_user_cover" view="ImageView"><Expressions/><location startLine="28" startOffset="16" endLine="34" endOffset="63"/></Target><Target id="@+id/view_gradient_overlay" view="View"><Expressions/><location startLine="37" startOffset="16" endLine="42" endOffset="63"/></Target><Target id="@+id/iv_user_avatar" view="de.hdodenhof.circleimageview.CircleImageView"><Expressions/><location startLine="45" startOffset="16" endLine="55" endOffset="63"/></Target><Target id="@+id/layout_user_info" view="LinearLayout"><Expressions/><location startLine="58" startOffset="16" endLine="129" endOffset="30"/></Target><Target id="@+id/tv_username" view="TextView"><Expressions/><location startLine="70" startOffset="20" endLine="77" endOffset="50"/></Target><Target id="@+id/layout_user_tags" view="LinearLayout"><Expressions/><location startLine="80" startOffset="20" endLine="116" endOffset="34"/></Target><Target id="@+id/tv_vip_tag" view="TextView"><Expressions/><location startLine="88" startOffset="24" endLine="102" endOffset="56"/></Target><Target id="@+id/tv_level_tag" view="TextView"><Expressions/><location startLine="105" startOffset="24" endLine="115" endOffset="53"/></Target><Target id="@+id/tv_signature" view="TextView"><Expressions/><location startLine="119" startOffset="20" endLine="128" endOffset="49"/></Target><Target id="@+id/card_user_stats" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="132" startOffset="16" endLine="272" endOffset="51"/></Target><Target id="@+id/tv_liked_songs_count" view="TextView"><Expressions/><location startLine="158" startOffset="28" endLine="165" endOffset="58"/></Target><Target id="@+id/tv_playlists_count" view="TextView"><Expressions/><location startLine="190" startOffset="28" endLine="197" endOffset="58"/></Target><Target id="@+id/tv_followed_artists_count" view="TextView"><Expressions/><location startLine="222" startOffset="28" endLine="229" endOffset="58"/></Target><Target id="@+id/tv_listening_hours" view="TextView"><Expressions/><location startLine="254" startOffset="28" endLine="261" endOffset="58"/></Target><Target id="@+id/tv_more" view="TextView"><Expressions/><location startLine="312" startOffset="16" endLine="318" endOffset="45"/></Target><Target id="@+id/tv_phone" view="TextView"><Expressions/><location startLine="348" startOffset="24" endLine="354" endOffset="53"/></Target><Target id="@+id/tv_email" view="TextView"><Expressions/><location startLine="376" startOffset="24" endLine="382" endOffset="53"/></Target><Target id="@+id/tv_vip_status" view="TextView"><Expressions/><location startLine="404" startOffset="24" endLine="410" endOffset="53"/></Target><Target id="@+id/tv_register_time" view="TextView"><Expressions/><location startLine="432" startOffset="24" endLine="438" endOffset="53"/></Target><Target id="@+id/btn_logout" view="Button"><Expressions/><location startLine="444" startOffset="12" endLine="451" endOffset="58"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="456" startOffset="4" endLine="461" endOffset="35"/></Target><Target id="@+id/tv_error" view="TextView"><Expressions/><location startLine="464" startOffset="4" endLine="471" endOffset="35"/></Target></Targets></Layout>