<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="page_player_playlist" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\page_player_playlist.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/page_player_playlist_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="39" endOffset="14"/></Target><Target id="@+id/text_playlist_title" view="TextView"><Expressions/><location startLine="12" startOffset="8" endLine="19" endOffset="38"/></Target><Target id="@+id/text_playlist_count" view="TextView"><Expressions/><location startLine="21" startOffset="8" endLine="29" endOffset="33"/></Target><Target id="@+id/recycler_view_playlist" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="33" startOffset="4" endLine="38" endOffset="39"/></Target></Targets></Layout>