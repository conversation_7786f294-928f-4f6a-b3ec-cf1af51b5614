<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_playlist_song" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\item_playlist_song.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/item_playlist_song_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="46" endOffset="14"/></Target><Target id="@+id/image_song_cover" view="ImageView"><Expressions/><location startLine="11" startOffset="4" endLine="17" endOffset="43"/></Target><Target id="@+id/text_song_title" view="TextView"><Expressions/><location startLine="25" startOffset="8" endLine="33" endOffset="31"/></Target><Target id="@+id/text_song_artist" view="TextView"><Expressions/><location startLine="35" startOffset="8" endLine="44" endOffset="31"/></Target></Targets></Layout>