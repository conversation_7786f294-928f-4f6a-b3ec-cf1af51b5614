#Mon May 26 13:47:47 CST 2025
com.example.aimusicplayer.app-main-63\:/drawable/ripple_circular_button_perfect.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ripple_circular_button_perfect.xml.flat
com.example.aimusicplayer.app-mergeReleaseResources-61\:/layout/item_search_suggestion.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_item_search_suggestion.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/button_primary.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_button_primary.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/bg_playing_disc.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_bg_playing_disc.xml.flat
com.example.aimusicplayer.app-main-63\:/mipmap-xxhdpi/cherry_blossom_car.webp=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxhdpi_cherry_blossom_car.webp.flat
com.example.aimusicplayer.app-mergeReleaseResources-61\:/layout/item_reply.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_item_reply.xml.flat
com.example.aimusicplayer.app-mergeReleaseResources-61\:/layout/page_player_lyrics.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_page_player_lyrics.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_play_mode_single.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_play_mode_single.xml.flat
com.example.aimusicplayer.app-mergeReleaseResources-61\:/layout/item_search_result.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_item_search_result.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_car.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_car.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_search.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_search.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/vip_label_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_vip_label_background.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_more_vert.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_more_vert.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_arrow_down.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_arrow_down.xml.flat
com.example.aimusicplayer.app-mergeReleaseResources-61\:/layout/item_search_suggest.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_item_search_suggest.xml.flat
com.example.aimusicplayer.app-main-63\:/mipmap-xxhdpi/ic_launcher.png=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxhdpi_ic_launcher.png.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_previous.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_previous.xml.flat
com.example.aimusicplayer.app-mergeReleaseResources-61\:/layout/dialog_playlist.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_dialog_playlist.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_search_white.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_search_white.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_favorite.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_favorite.xml.flat
com.example.aimusicplayer.app-main-63\:/mipmap-xhdpi/cherry_blossom_car.webp=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xhdpi_cherry_blossom_car.webp.flat
com.example.aimusicplayer.app-main-63\:/drawable/icon_heart.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_icon_heart.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_playlist.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_playlist.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_like_outline.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_like_outline.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/bg_vip_tag.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_bg_vip_tag.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_tonearm.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_tonearm.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/splash_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_splash_background.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/bg_button_primary.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_bg_button_primary.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/splash_gradient.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_splash_gradient.xml.flat
com.example.aimusicplayer.app-mergeReleaseResources-61\:/layout/player_controls.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_player_controls.xml.flat
com.example.aimusicplayer.app-main-63\:/anim/layout_animation_fall_down.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_layout_animation_fall_down.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/search_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_search_background.xml.flat
com.example.aimusicplayer.app-main-63\:/mipmap-mdpi/cherry_blossom_car_round.webp=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-mdpi_cherry_blossom_car_round.webp.flat
com.example.aimusicplayer.app-main-63\:/anim/fade_in.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_fade_in.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/icon_car.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_icon_car.xml.flat
com.example.aimusicplayer.app-main-63\:/anim/rotate_pause_to_play.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_rotate_pause_to_play.xml.flat
com.example.aimusicplayer.app-mergeReleaseResources-61\:/layout/view_lottie_loading.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_view_lottie_loading.xml.flat
com.example.aimusicplayer.app-mergeReleaseResources-61\:/layout/dialog_intelligence.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_dialog_intelligence.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_like_filled.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_like_filled.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_qr_placeholder.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_qr_placeholder.xml.flat
com.example.aimusicplayer.app-mergeReleaseResources-61\:/layout/fragment_driving_mode.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_fragment_driving_mode.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/rounded_status_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_rounded_status_background.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_driving.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_driving.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_user.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_user.xml.flat
com.example.aimusicplayer.app-mergeReleaseResources-61\:/layout/dialog_play_queue.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_dialog_play_queue.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ripple_circular_button.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ripple_circular_button.xml.flat
com.example.aimusicplayer.app-main-63\:/xml/automotive_app_desc.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\xml_automotive_app_desc.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/vinyl_border.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_vinyl_border.xml.flat
com.example.aimusicplayer.app-main-63\:/mipmap-mdpi/ic_launcher.png=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-mdpi_ic_launcher.png.flat
com.example.aimusicplayer.app-main-63\:/drawable/modern_login_button.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_modern_login_button.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_favorite_selector.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_favorite_selector.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/bg_comment_input.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_bg_comment_input.xml.flat
com.example.aimusicplayer.app-mergeReleaseResources-61\:/layout/dialog_qr_login.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_dialog_qr_login.xml.flat
com.example.aimusicplayer.app-mergeReleaseResources-61\:/layout/dialog_heart_mode.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_dialog_heart_mode.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_repeat_all.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_repeat_all.xml.flat
com.example.aimusicplayer.app-main-63\:/mipmap-hdpi/ic_launcher.png=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-hdpi_ic_launcher.png.flat
com.example.aimusicplayer.app-main-63\:/drawable/circle_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_circle_background.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/vinyl_center.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_vinyl_center.xml.flat
com.example.aimusicplayer.app-main-63\:/anim/rotate.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_rotate.xml.flat
com.example.aimusicplayer.app-mergeReleaseResources-61\:/layout/fragment_comment.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_fragment_comment.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ripple_compact_button.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ripple_compact_button.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/sidebar_sakura_bg.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_sidebar_sakura_bg.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_queue_music.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_queue_music.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_tonearm_new.png=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_tonearm_new.png.flat
com.example.aimusicplayer.app-main-63\:/anim/fade_out.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_fade_out.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_arrow_right.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_arrow_right.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_phone_login.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_phone_login.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/dark_blue_gradient_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_dark_blue_gradient_background.xml.flat
com.example.aimusicplayer.app-main-63\:/anim/fragment_fade_out.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_fragment_fade_out.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/cherry_blossom_car.jpg=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_cherry_blossom_car.jpg.flat
com.example.aimusicplayer.app-main-63\:/drawable/icon_settings.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_icon_settings.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/default_avatar.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_default_avatar.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/dialog_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_dialog_background.xml.flat
com.example.aimusicplayer.app-main-63\:/anim/slide_in_up.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_slide_in_up.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_play_mode_loop.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_play_mode_loop.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/bg_playing_cover_border.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_bg_playing_cover_border.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_back_improved.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_back_improved.xml.flat
com.example.aimusicplayer.app-mergeReleaseResources-61\:/layout/dialog_comment.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_dialog_comment.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/control_button_animated.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_control_button_animated.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/sakura_button.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_sakura_button.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_settings.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_settings.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_qrcode_login.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_qrcode_login.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/play_button_click_animation.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_play_button_click_animation.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/bg_button.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_bg_button.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_playing_play_pause_selector.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_playing_play_pause_selector.xml.flat
com.example.aimusicplayer.app-mergeReleaseResources-61\:/layout/fragment_placeholder.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_fragment_placeholder.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_profile.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_profile.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/button_secondary_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_button_secondary_background.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_intelligence.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_intelligence.xml.flat
com.example.aimusicplayer.app-main-63\:/anim/item_animation_comment_fade_in.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_item_animation_comment_fade_in.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/button_playlist_close.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_button_playlist_close.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_random.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_random.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_music.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_music.xml.flat
com.example.aimusicplayer.app-main-63\:/xml/data_extraction_rules.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\xml_data_extraction_rules.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/button_secondary.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_button_secondary.xml.flat
com.example.aimusicplayer.app-main-63\:/anim/page_transition_fade.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_page_transition_fade.xml.flat
com.example.aimusicplayer.app-main-63\:/anim/slide_in_down.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_slide_in_down.xml.flat
com.example.aimusicplayer.app-mergeReleaseResources-61\:/layout/item_top_list.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_item_top_list.xml.flat
com.example.aimusicplayer.app-main-63\:/anim/button_press.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_button_press.xml.flat
com.example.aimusicplayer.app-mergeReleaseResources-61\:/layout/activity_splash.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_activity_splash.xml.flat
com.example.aimusicplayer.app-main-63\:/anim/shake.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_shake.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/next.png=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_next.png.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_heart.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_heart.xml.flat
com.example.aimusicplayer.app-main-63\:/anim/fragment_fade_in.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_fragment_fade_in.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/bg_playing_playback_progress.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_bg_playing_playback_progress.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/sakura_search_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_sakura_search_background.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/comment_input_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_comment_input_background.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/gradient_blue_purple.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_gradient_blue_purple.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/round_button_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_round_button_background.xml.flat
com.example.aimusicplayer.app-mergeReleaseResources-61\:/layout/item_play_queue.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_item_play_queue.xml.flat
com.example.aimusicplayer.app-main-63\:/mipmap-xhdpi/cherry_blossom_car_foreground.webp=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xhdpi_cherry_blossom_car_foreground.webp.flat
com.example.aimusicplayer.app-mergeReleaseResources-61\:/layout/fragment_user_profile.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_fragment_user_profile.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_menu.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_menu.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_close.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_close.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/nav_button_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_nav_button_background.xml.flat
com.example.aimusicplayer.app-main-63\:/anim/comment_like_animation.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_comment_like_animation.xml.flat
com.example.aimusicplayer.app-main-63\:/anim/slide_out_right.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_slide_out_right.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_volume_up.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_volume_up.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/player_control_bg.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_player_control_bg.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/button_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_button_background.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/sakura_play_button_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_sakura_play_button_background.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_arrow_back.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_arrow_back.xml.flat
com.example.aimusicplayer.app-mergeReleaseResources-61\:/layout/item_comment.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_item_comment.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_back.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_back.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/icon_user.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_icon_user.xml.flat
com.example.aimusicplayer.app-mergeReleaseResources-61\:/layout/view_album_cover.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_view_album_cover.xml.flat
com.example.aimusicplayer.app-main-63\:/mipmap-hdpi/cherry_blossom_car_foreground.webp=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-hdpi_cherry_blossom_car_foreground.webp.flat
com.example.aimusicplayer.app-main-63\:/drawable/pause.png=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_pause.png.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_globe.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_globe.xml.flat
com.example.aimusicplayer.app-mergeReleaseResources-61\:/layout/activity_login.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_activity_login.xml.flat
com.example.aimusicplayer.app-mergeReleaseResources-61\:/layout/page_player_playlist.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_page_player_playlist.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_repeat_off.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_repeat_off.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/edit_text_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_edit_text_background.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_music_note.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_music_note.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/sakura_dialog_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_sakura_dialog_background.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/vinyl_record.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_vinyl_record.xml.flat
com.example.aimusicplayer.app-main-63\:/anim/item_animation_comment_slide_up.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_item_animation_comment_slide_up.xml.flat
com.example.aimusicplayer.app-main-63\:/anim/layout_animation_comment_list.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_layout_animation_comment_list.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_equalizer.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_equalizer.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_playing_playback_progress_thumb.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_playing_playback_progress_thumb.xml.flat
com.example.aimusicplayer.app-main-63\:/anim/item_animation_fall_down.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_item_animation_fall_down.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/login_button_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_login_button_background.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/vinyl_arm.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_vinyl_arm.xml.flat
com.example.aimusicplayer.app-main-63\:/mipmap-xxhdpi/cherry_blossom_car_foreground.webp=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxhdpi_cherry_blossom_car_foreground.webp.flat
com.example.aimusicplayer.app-mergeReleaseResources-61\:/layout/item_song.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_item_song.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/sakura_search_results_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_sakura_search_results_background.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_guest_login.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_guest_login.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/icon_music.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_icon_music.xml.flat
com.example.aimusicplayer.app-main-63\:/mipmap-xhdpi/cherry_blossom_car_round.webp=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xhdpi_cherry_blossom_car_round.webp.flat
com.example.aimusicplayer.app-main-63\:/drawable/gradient_overlay.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_gradient_overlay.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/custom_app_icon.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_custom_app_icon.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_default_cover.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_default_cover.xml.flat
com.example.aimusicplayer.app-main-63\:/anim/rotate_play_to_pause.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_rotate_play_to_pause.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_repeat.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_repeat.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/bg_bottom_sheet.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_bg_bottom_sheet.xml.flat
com.example.aimusicplayer.app-main-63\:/anim/item_animation_from_bottom.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_item_animation_from_bottom.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_shuffle.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_shuffle.xml.flat
com.example.aimusicplayer.app-main-63\:/xml/backup_rules.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\xml_backup_rules.xml.flat
com.example.aimusicplayer.app-main-63\:/anim/slide_out_down.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_slide_out_down.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/control_button_no_ripple.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_control_button_no_ripple.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/logo.jpg=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_logo.jpg.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_launcher_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_launcher_background.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_comment.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_comment.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/circle_background_top3.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_circle_background_top3.xml.flat
com.example.aimusicplayer.app-main-63\:/anim/slide_in_left.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_slide_in_left.xml.flat
com.example.aimusicplayer.app-main-63\:/anim/button_release_feedback.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_button_release_feedback.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/compact_button_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_compact_button_background.xml.flat
com.example.aimusicplayer.app-main-63\:/mipmap-hdpi/cherry_blossom_car_round.webp=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-hdpi_cherry_blossom_car_round.webp.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_grid.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_grid.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/back_button_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_back_button_background.xml.flat
com.example.aimusicplayer.app-main-63\:/mipmap-xxhdpi/cherry_blossom_car_round.webp=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxhdpi_cherry_blossom_car_round.webp.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_library.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_library.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_repeat_one.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_repeat_one.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_play.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_play.xml.flat
com.example.aimusicplayer.app-main-63\:/anim/slide_out_left.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_slide_out_left.xml.flat
com.example.aimusicplayer.app-main-63\:/anim/scale_down.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_scale_down.xml.flat
com.example.aimusicplayer.app-main-63\:/mipmap-xhdpi/ic_launcher.png=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xhdpi_ic_launcher.png.flat
com.example.aimusicplayer.app-main-63\:/drawable/round_menu_button_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_round_menu_button_background.xml.flat
com.example.aimusicplayer.app-mergeReleaseResources-61\:/layout/fragment_player.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_fragment_player.xml.flat
com.example.aimusicplayer.app-main-63\:/mipmap-hdpi/cherry_blossom_car.webp=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-hdpi_cherry_blossom_car.webp.flat
com.example.aimusicplayer.app-main-63\:/mipmap-mdpi/cherry_blossom_car.webp=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-mdpi_cherry_blossom_car.webp.flat
com.example.aimusicplayer.app-mergeReleaseResources-61\:/layout/activity_main.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_activity_main.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/default_album_art.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_default_album_art.xml.flat
com.example.aimusicplayer.app-main-63\:/anim/scale_up.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_scale_up.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/cherry_blossom_car_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_cherry_blossom_car_background.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_playing_needle.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_playing_needle.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_next.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_next.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_play_mode_level_list.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_play_mode_level_list.xml.flat
com.example.aimusicplayer.app-main-63\:/anim/slide_up.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_slide_up.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/search_results_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_search_results_background.xml.flat
com.example.aimusicplayer.app-main-63\:/xml/network_security_config.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\xml_network_security_config.xml.flat
com.example.aimusicplayer.app-main-63\:/mipmap-anydpi-v26/cherry_blossom_car_round.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-anydpi-v26_cherry_blossom_car_round.xml.flat
com.example.aimusicplayer.app-main-63\:/mipmap-xxxhdpi/cherry_blossom_car_foreground.webp=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxxhdpi_cherry_blossom_car_foreground.webp.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_arrow_left.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_arrow_left.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/control_button_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_control_button_background.xml.flat
com.example.aimusicplayer.app-mergeReleaseResources-61\:/layout/item_online_song.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_item_online_song.xml.flat
com.example.aimusicplayer.app-main-63\:/mipmap-anydpi-v26/cherry_blossom_car.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-anydpi-v26_cherry_blossom_car.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_share.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_share.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_play_order.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_play_order.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/default_cover.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_default_cover.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/sakura_button_secondary.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_sakura_button_secondary.xml.flat
com.example.aimusicplayer.app-main-63\:/anim/slide_in_right.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_slide_in_right.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_vinyl_bg.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_vinyl_bg.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/bg_edit_text.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_bg_edit_text.xml.flat
com.example.aimusicplayer.app-main-63\:/anim/rotate_album.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_rotate_album.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable-v24/ic_launcher_foreground.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v24_ic_launcher_foreground.xml.flat
com.example.aimusicplayer.app-main-63\:/navigation/nav_graph.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\navigation_nav_graph.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_player.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_player.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/previous.png=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_previous.png.flat
com.example.aimusicplayer.app-main-63\:/mipmap-xxxhdpi/cherry_blossom_car.webp=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxxhdpi_cherry_blossom_car.webp.flat
com.example.aimusicplayer.app-main-63\:/drawable/default_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_default_background.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_notification.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_notification.xml.flat
com.example.aimusicplayer.app-main-63\:/xml/file_paths.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\xml_file_paths.xml.flat
com.example.aimusicplayer.app-mergeReleaseResources-61\:/layout/item_hot_search.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_item_hot_search.xml.flat
com.example.aimusicplayer.app-mergeReleaseResources-61\:/layout/item_comment_header.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_item_comment_header.xml.flat
com.example.aimusicplayer.app-main-63\:/mipmap-xxxhdpi/cherry_blossom_car_round.webp=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxxhdpi_cherry_blossom_car_round.webp.flat
com.example.aimusicplayer.app-main-63\:/drawable/search_background_compact.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_search_background_compact.xml.flat
com.example.aimusicplayer.app-mergeReleaseResources-61\:/layout/item_playlist.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_item_playlist.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/logo_music.png=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_logo_music.png.flat
com.example.aimusicplayer.app-main-63\:/drawable/play.png=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_play.png.flat
com.example.aimusicplayer.app-main-63\:/drawable/search_suggestions_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_search_suggestions_background.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/sakura_edit_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_sakura_edit_background.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/icon_globe.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_icon_globe.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_heart_mode.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_heart_mode.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_play_mode_shuffle.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_play_mode_shuffle.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/search_button_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_search_button_background.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_pause.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_pause.xml.flat
com.example.aimusicplayer.app-main-63\:/mipmap-mdpi/cherry_blossom_car_foreground.webp=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-mdpi_cherry_blossom_car_foreground.webp.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_discovery.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_discovery.xml.flat
com.example.aimusicplayer.app-main-63\:/anim/comment_send_success.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_comment_send_success.xml.flat
com.example.aimusicplayer.app-mergeReleaseResources-61\:/layout/item_playlist_song.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_item_playlist_song.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_check_circle.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_check_circle.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_clear_all.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_clear_all.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/round_icon_bg.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_round_icon_bg.xml.flat
com.example.aimusicplayer.app-main-63\:/anim/button_press_feedback.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_button_press_feedback.xml.flat
com.example.aimusicplayer.app-mergeReleaseResources-61\:/layout/fragment_intelligence.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_fragment_intelligence.xml.flat
com.example.aimusicplayer.app-mergeReleaseResources-61\:/layout/dialog_phone_login.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_dialog_phone_login.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/button_primary_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_button_primary_background.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ripple_oval_button.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ripple_oval_button.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_refresh.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_refresh.xml.flat
com.example.aimusicplayer.app-main-63\:/mipmap-xxxhdpi/ic_launcher.png=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxxhdpi_ic_launcher.png.flat
com.example.aimusicplayer.app-main-63\:/drawable/album_art_border.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_album_art_border.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_favorite_border.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_favorite_border.xml.flat
com.example.aimusicplayer.app-main-63\:/anim/reset_rotation.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_reset_rotation.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_qr_error.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_qr_error.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/sakura_search_button_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_sakura_search_button_background.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_needle.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_needle.xml.flat
com.example.aimusicplayer.app-main-63\:/anim/slide_out_up.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_slide_out_up.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/sakura_search_suggestions_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_sakura_search_suggestions_background.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/ic_play_small.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_play_small.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/bg_comment_success.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_bg_comment_success.xml.flat
com.example.aimusicplayer.app-mergeReleaseResources-61\:/layout/page_player_comments.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_page_player_comments.xml.flat
com.example.aimusicplayer.app-main-63\:/anim/item_animation_from_right.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_item_animation_from_right.xml.flat
com.example.aimusicplayer.app-main-63\:/drawable/button_rounded.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_button_rounded.xml.flat
