#Mon May 26 13:47:53 CST 2025
com.example.aimusicplayer.app-main-4\:/drawable/ic_share.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_share.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_play_mode_level_list.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_play_mode_level_list.xml
com.example.aimusicplayer.app-main-4\:/anim/item_animation_from_bottom.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\anim\\item_animation_from_bottom.xml
com.example.aimusicplayer.app-main-4\:/drawable/bg_button.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\bg_button.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_tonearm.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_tonearm.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_qrcode_login.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_qrcode_login.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_refresh.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_refresh.xml
com.example.aimusicplayer.app-main-4\:/drawable-v24/ic_launcher_foreground.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-v24\\ic_launcher_foreground.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_back_improved.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_back_improved.xml
com.example.aimusicplayer.app-main-4\:/drawable/vip_label_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\vip_label_background.xml
com.example.aimusicplayer.app-main-4\:/drawable/icon_heart.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\icon_heart.xml
com.example.aimusicplayer.app-packageReleaseResources-2\:/layout/view_lottie_loading.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\view_lottie_loading.xml
com.example.aimusicplayer.app-packageReleaseResources-2\:/layout/dialog_intelligence.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\dialog_intelligence.xml
com.example.aimusicplayer.app-main-4\:/anim/scale_down.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\anim\\scale_down.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_qr_error.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_qr_error.xml
com.example.aimusicplayer.app-main-4\:/drawable/previous.png=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\previous.png
com.example.aimusicplayer.app-main-4\:/anim/layout_animation_comment_list.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\anim\\layout_animation_comment_list.xml
com.example.aimusicplayer.app-main-4\:/drawable/cherry_blossom_car.jpg=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\cherry_blossom_car.jpg
com.example.aimusicplayer.app-main-4\:/anim/rotate_album.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\anim\\rotate_album.xml
com.example.aimusicplayer.app-main-4\:/anim/rotate_pause_to_play.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\anim\\rotate_pause_to_play.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_player.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_player.xml
com.example.aimusicplayer.app-main-4\:/drawable/play_button_click_animation.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\play_button_click_animation.xml
com.example.aimusicplayer.app-main-4\:/drawable/pause.png=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\pause.png
com.example.aimusicplayer.app-packageReleaseResources-2\:/layout/item_playlist_song.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\item_playlist_song.xml
com.example.aimusicplayer.app-packageReleaseResources-2\:/layout/dialog_play_queue.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\dialog_play_queue.xml
com.example.aimusicplayer.app-packageReleaseResources-2\:/layout/item_reply.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\item_reply.xml
com.example.aimusicplayer.app-main-4\:/mipmap-xhdpi/cherry_blossom_car_foreground.webp=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xhdpi-v4\\cherry_blossom_car_foreground.webp
com.example.aimusicplayer.app-main-4\:/drawable/ic_user.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_user.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_close.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_close.xml
com.example.aimusicplayer.app-main-4\:/anim/comment_like_animation.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\anim\\comment_like_animation.xml
com.example.aimusicplayer.app-main-4\:/drawable/icon_user.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\icon_user.xml
com.example.aimusicplayer.app-main-4\:/mipmap-xxhdpi/ic_launcher.png=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xxhdpi-v4\\ic_launcher.png
com.example.aimusicplayer.app-main-4\:/drawable/ic_music.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_music.xml
com.example.aimusicplayer.app-main-4\:/drawable/sakura_button_secondary.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\sakura_button_secondary.xml
com.example.aimusicplayer.app-packageReleaseResources-2\:/layout/activity_main.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_main.xml
com.example.aimusicplayer.app-main-4\:/anim/shake.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\anim\\shake.xml
com.example.aimusicplayer.app-main-4\:/drawable/sakura_button.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\sakura_button.xml
com.example.aimusicplayer.app-packageReleaseResources-2\:/layout/item_play_queue.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\item_play_queue.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_play_mode_single.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_play_mode_single.xml
com.example.aimusicplayer.app-main-4\:/drawable/default_avatar.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\default_avatar.xml
com.example.aimusicplayer.app-main-4\:/anim/slide_in_right.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\anim\\slide_in_right.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_vinyl_bg.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_vinyl_bg.xml
com.example.aimusicplayer.app-main-4\:/drawable/vinyl_center.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\vinyl_center.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_more_vert.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_more_vert.xml
com.example.aimusicplayer.app-main-4\:/anim/reset_rotation.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\anim\\reset_rotation.xml
com.example.aimusicplayer.app-main-4\:/anim/rotate.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\anim\\rotate.xml
com.example.aimusicplayer.app-packageReleaseResources-2\:/layout/page_player_playlist.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\page_player_playlist.xml
com.example.aimusicplayer.app-packageReleaseResources-2\:/layout/dialog_phone_login.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\dialog_phone_login.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_play_mode_shuffle.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_play_mode_shuffle.xml
com.example.aimusicplayer.app-main-4\:/drawable/vinyl_border.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\vinyl_border.xml
com.example.aimusicplayer.app-main-4\:/anim/item_animation_comment_fade_in.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\anim\\item_animation_comment_fade_in.xml
com.example.aimusicplayer.app-main-4\:/mipmap-xxhdpi/cherry_blossom_car.webp=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xxhdpi-v4\\cherry_blossom_car.webp
com.example.aimusicplayer.app-main-4\:/mipmap-xxhdpi/cherry_blossom_car_foreground.webp=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xxhdpi-v4\\cherry_blossom_car_foreground.webp
com.example.aimusicplayer.app-packageReleaseResources-2\:/layout/item_comment.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\item_comment.xml
com.example.aimusicplayer.app-main-4\:/mipmap-hdpi/cherry_blossom_car_round.webp=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-hdpi-v4\\cherry_blossom_car_round.webp
com.example.aimusicplayer.app-main-4\:/xml/network_security_config.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\xml\\network_security_config.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_library.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_library.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_settings.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_settings.xml
com.example.aimusicplayer.app-main-4\:/mipmap-anydpi-v26/cherry_blossom_car_round.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-anydpi-v26\\cherry_blossom_car_round.xml
com.example.aimusicplayer.app-main-4\:/navigation/nav_graph.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\navigation\\nav_graph.xml
com.example.aimusicplayer.app-main-4\:/drawable/button_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\button_background.xml
com.example.aimusicplayer.app-main-4\:/drawable/vinyl_arm.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\vinyl_arm.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_search.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_search.xml
com.example.aimusicplayer.app-main-4\:/drawable/bg_playing_disc.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\bg_playing_disc.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_playing_playback_progress_thumb.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_playing_playback_progress_thumb.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_playing_needle.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_playing_needle.xml
com.example.aimusicplayer.app-main-4\:/drawable/compact_button_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\compact_button_background.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_heart.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_heart.xml
com.example.aimusicplayer.app-main-4\:/drawable/icon_settings.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\icon_settings.xml
com.example.aimusicplayer.app-packageReleaseResources-2\:/layout/item_hot_search.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\item_hot_search.xml
com.example.aimusicplayer.app-main-4\:/anim/slide_out_right.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\anim\\slide_out_right.xml
com.example.aimusicplayer.app-main-4\:/anim/slide_out_down.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\anim\\slide_out_down.xml
com.example.aimusicplayer.app-packageReleaseResources-2\:/layout/activity_splash.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_splash.xml
com.example.aimusicplayer.app-main-4\:/drawable/button_playlist_close.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\button_playlist_close.xml
com.example.aimusicplayer.app-main-4\:/mipmap-hdpi/cherry_blossom_car_foreground.webp=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-hdpi-v4\\cherry_blossom_car_foreground.webp
com.example.aimusicplayer.app-main-4\:/anim/button_press.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\anim\\button_press.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_menu.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_menu.xml
com.example.aimusicplayer.app-main-4\:/drawable/bg_edit_text.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\bg_edit_text.xml
com.example.aimusicplayer.app-main-4\:/drawable/sakura_search_suggestions_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\sakura_search_suggestions_background.xml
com.example.aimusicplayer.app-main-4\:/mipmap-anydpi-v26/cherry_blossom_car.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-anydpi-v26\\cherry_blossom_car.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_favorite_selector.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_favorite_selector.xml
com.example.aimusicplayer.app-main-4\:/drawable/custom_app_icon.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\custom_app_icon.xml
com.example.aimusicplayer.app-main-4\:/drawable/login_button_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\login_button_background.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_repeat_off.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_repeat_off.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_car.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_car.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_launcher_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_launcher_background.xml
com.example.aimusicplayer.app-main-4\:/mipmap-xxhdpi/cherry_blossom_car_round.webp=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xxhdpi-v4\\cherry_blossom_car_round.webp
com.example.aimusicplayer.app-packageReleaseResources-2\:/layout/fragment_driving_mode.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\fragment_driving_mode.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_queue_music.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_queue_music.xml
com.example.aimusicplayer.app-main-4\:/drawable/player_control_bg.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\player_control_bg.xml
com.example.aimusicplayer.app-packageReleaseResources-2\:/layout/item_top_list.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\item_top_list.xml
com.example.aimusicplayer.app-main-4\:/mipmap-mdpi/cherry_blossom_car.webp=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-mdpi-v4\\cherry_blossom_car.webp
com.example.aimusicplayer.app-main-4\:/xml/file_paths.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\xml\\file_paths.xml
com.example.aimusicplayer.app-main-4\:/drawable/back_button_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\back_button_background.xml
com.example.aimusicplayer.app-main-4\:/anim/fragment_fade_out.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\anim\\fragment_fade_out.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_clear_all.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_clear_all.xml
com.example.aimusicplayer.app-packageReleaseResources-2\:/layout/dialog_qr_login.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\dialog_qr_login.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_like_filled.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_like_filled.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_discovery.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_discovery.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_repeat.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_repeat.xml
com.example.aimusicplayer.app-main-4\:/drawable/sakura_edit_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\sakura_edit_background.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_arrow_right.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_arrow_right.xml
com.example.aimusicplayer.app-main-4\:/anim/layout_animation_fall_down.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\anim\\layout_animation_fall_down.xml
com.example.aimusicplayer.app-main-4\:/drawable/circle_background_top3.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\circle_background_top3.xml
com.example.aimusicplayer.app-packageReleaseResources-2\:/layout/fragment_comment.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\fragment_comment.xml
com.example.aimusicplayer.app-main-4\:/drawable/ripple_compact_button.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ripple_compact_button.xml
com.example.aimusicplayer.app-main-4\:/drawable/play.png=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\play.png
com.example.aimusicplayer.app-packageReleaseResources-2\:/layout/activity_login.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_login.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_play_mode_loop.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_play_mode_loop.xml
com.example.aimusicplayer.app-main-4\:/anim/slide_out_left.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\anim\\slide_out_left.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_repeat_all.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_repeat_all.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_notification.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_notification.xml
com.example.aimusicplayer.app-main-4\:/drawable/ripple_circular_button_perfect.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ripple_circular_button_perfect.xml
com.example.aimusicplayer.app-packageReleaseResources-2\:/layout/item_comment_header.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\item_comment_header.xml
com.example.aimusicplayer.app-main-4\:/drawable/sakura_search_results_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\sakura_search_results_background.xml
com.example.aimusicplayer.app-main-4\:/drawable/logo.jpg=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\logo.jpg
com.example.aimusicplayer.app-main-4\:/xml/data_extraction_rules.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\xml\\data_extraction_rules.xml
com.example.aimusicplayer.app-main-4\:/drawable/sakura_search_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\sakura_search_background.xml
com.example.aimusicplayer.app-main-4\:/xml/backup_rules.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\xml\\backup_rules.xml
com.example.aimusicplayer.app-main-4\:/drawable/rounded_status_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\rounded_status_background.xml
com.example.aimusicplayer.app-main-4\:/drawable/vinyl_record.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\vinyl_record.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_intelligence.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_intelligence.xml
com.example.aimusicplayer.app-main-4\:/drawable/default_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\default_background.xml
com.example.aimusicplayer.app-main-4\:/drawable/sakura_play_button_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\sakura_play_button_background.xml
com.example.aimusicplayer.app-main-4\:/anim/scale_up.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\anim\\scale_up.xml
com.example.aimusicplayer.app-main-4\:/mipmap-xxxhdpi/cherry_blossom_car_foreground.webp=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xxxhdpi-v4\\cherry_blossom_car_foreground.webp
com.example.aimusicplayer.app-main-4\:/xml/automotive_app_desc.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\xml\\automotive_app_desc.xml
com.example.aimusicplayer.app-main-4\:/drawable/edit_text_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\edit_text_background.xml
com.example.aimusicplayer.app-main-4\:/drawable/search_button_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\search_button_background.xml
com.example.aimusicplayer.app-packageReleaseResources-2\:/layout/item_search_suggestion.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\item_search_suggestion.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_needle.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_needle.xml
com.example.aimusicplayer.app-main-4\:/drawable/bg_playing_playback_progress.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\bg_playing_playback_progress.xml
com.example.aimusicplayer.app-main-4\:/anim/slide_in_left.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\anim\\slide_in_left.xml
com.example.aimusicplayer.app-main-4\:/drawable/button_primary_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\button_primary_background.xml
com.example.aimusicplayer.app-packageReleaseResources-2\:/layout/fragment_intelligence.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\fragment_intelligence.xml
com.example.aimusicplayer.app-main-4\:/drawable/round_icon_bg.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\round_icon_bg.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_shuffle.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_shuffle.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_favorite_border.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_favorite_border.xml
com.example.aimusicplayer.app-main-4\:/drawable/icon_music.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\icon_music.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_back.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_back.xml
com.example.aimusicplayer.app-main-4\:/drawable/sakura_dialog_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\sakura_dialog_background.xml
com.example.aimusicplayer.app-main-4\:/anim/item_animation_fall_down.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\anim\\item_animation_fall_down.xml
com.example.aimusicplayer.app-main-4\:/drawable/control_button_animated.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\control_button_animated.xml
com.example.aimusicplayer.app-main-4\:/anim/button_release_feedback.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\anim\\button_release_feedback.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_arrow_back.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_arrow_back.xml
com.example.aimusicplayer.app-main-4\:/anim/fade_in.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\anim\\fade_in.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_play_small.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_play_small.xml
com.example.aimusicplayer.app-main-4\:/drawable/ripple_oval_button.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ripple_oval_button.xml
com.example.aimusicplayer.app-main-4\:/drawable/modern_login_button.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\modern_login_button.xml
com.example.aimusicplayer.app-main-4\:/anim/item_animation_comment_slide_up.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\anim\\item_animation_comment_slide_up.xml
com.example.aimusicplayer.app-main-4\:/anim/slide_out_up.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\anim\\slide_out_up.xml
com.example.aimusicplayer.app-packageReleaseResources-2\:/layout/view_album_cover.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\view_album_cover.xml
com.example.aimusicplayer.app-main-4\:/drawable/dialog_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\dialog_background.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_previous.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_previous.xml
com.example.aimusicplayer.app-main-4\:/drawable/nav_button_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\nav_button_background.xml
com.example.aimusicplayer.app-main-4\:/drawable/album_art_border.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\album_art_border.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_grid.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_grid.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_like_outline.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_like_outline.xml
com.example.aimusicplayer.app-main-4\:/anim/slide_in_up.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\anim\\slide_in_up.xml
com.example.aimusicplayer.app-main-4\:/drawable/logo_music.png=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\logo_music.png
com.example.aimusicplayer.app-main-4\:/drawable/button_secondary.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\button_secondary.xml
com.example.aimusicplayer.app-main-4\:/drawable/circle_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\circle_background.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_arrow_down.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_arrow_down.xml
com.example.aimusicplayer.app-main-4\:/drawable/icon_globe.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\icon_globe.xml
com.example.aimusicplayer.app-main-4\:/anim/comment_send_success.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\anim\\comment_send_success.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_qr_placeholder.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_qr_placeholder.xml
com.example.aimusicplayer.app-packageReleaseResources-2\:/layout/page_player_comments.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\page_player_comments.xml
com.example.aimusicplayer.app-main-4\:/anim/page_transition_fade.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\anim\\page_transition_fade.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_equalizer.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_equalizer.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_play.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_play.xml
com.example.aimusicplayer.app-main-4\:/drawable/round_menu_button_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\round_menu_button_background.xml
com.example.aimusicplayer.app-main-4\:/drawable/gradient_blue_purple.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\gradient_blue_purple.xml
com.example.aimusicplayer.app-packageReleaseResources-2\:/layout/fragment_placeholder.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\fragment_placeholder.xml
com.example.aimusicplayer.app-main-4\:/drawable/search_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\search_background.xml
com.example.aimusicplayer.app-main-4\:/mipmap-xhdpi/cherry_blossom_car_round.webp=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xhdpi-v4\\cherry_blossom_car_round.webp
com.example.aimusicplayer.app-main-4\:/drawable/ic_favorite.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_favorite.xml
com.example.aimusicplayer.app-main-4\:/drawable/sidebar_sakura_bg.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\sidebar_sakura_bg.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_tonearm_new.png=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_tonearm_new.png
com.example.aimusicplayer.app-main-4\:/drawable/sakura_search_button_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\sakura_search_button_background.xml
com.example.aimusicplayer.app-main-4\:/anim/item_animation_from_right.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\anim\\item_animation_from_right.xml
com.example.aimusicplayer.app-main-4\:/drawable/comment_input_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\comment_input_background.xml
com.example.aimusicplayer.app-packageReleaseResources-2\:/layout/item_search_result.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\item_search_result.xml
com.example.aimusicplayer.app-main-4\:/drawable/button_rounded.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\button_rounded.xml
com.example.aimusicplayer.app-packageReleaseResources-2\:/layout/item_song.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\item_song.xml
com.example.aimusicplayer.app-main-4\:/drawable/search_background_compact.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\search_background_compact.xml
com.example.aimusicplayer.app-main-4\:/anim/button_press_feedback.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\anim\\button_press_feedback.xml
com.example.aimusicplayer.app-main-4\:/drawable/gradient_overlay.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\gradient_overlay.xml
com.example.aimusicplayer.app-main-4\:/drawable/next.png=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\next.png
com.example.aimusicplayer.app-main-4\:/drawable/ic_comment.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_comment.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_globe.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_globe.xml
com.example.aimusicplayer.app-main-4\:/drawable/default_album_art.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\default_album_art.xml
com.example.aimusicplayer.app-main-4\:/drawable/dark_blue_gradient_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\dark_blue_gradient_background.xml
com.example.aimusicplayer.app-packageReleaseResources-2\:/layout/fragment_user_profile.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\fragment_user_profile.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_profile.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_profile.xml
com.example.aimusicplayer.app-main-4\:/drawable/bg_bottom_sheet.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\bg_bottom_sheet.xml
com.example.aimusicplayer.app-main-4\:/anim/slide_in_down.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\anim\\slide_in_down.xml
com.example.aimusicplayer.app-main-4\:/mipmap-mdpi/cherry_blossom_car_round.webp=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-mdpi-v4\\cherry_blossom_car_round.webp
com.example.aimusicplayer.app-main-4\:/drawable/ic_default_cover.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_default_cover.xml
com.example.aimusicplayer.app-main-4\:/drawable/control_button_no_ripple.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\control_button_no_ripple.xml
com.example.aimusicplayer.app-packageReleaseResources-2\:/layout/item_playlist.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\item_playlist.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_volume_up.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_volume_up.xml
com.example.aimusicplayer.app-main-4\:/drawable/cherry_blossom_car_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\cherry_blossom_car_background.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_check_circle.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_check_circle.xml
com.example.aimusicplayer.app-main-4\:/drawable/bg_comment_input.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\bg_comment_input.xml
com.example.aimusicplayer.app-main-4\:/drawable/bg_button_primary.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\bg_button_primary.xml
com.example.aimusicplayer.app-main-4\:/drawable/ripple_circular_button.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ripple_circular_button.xml
com.example.aimusicplayer.app-main-4\:/anim/fade_out.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\anim\\fade_out.xml
com.example.aimusicplayer.app-main-4\:/drawable/splash_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\splash_background.xml
com.example.aimusicplayer.app-main-4\:/mipmap-xxxhdpi/cherry_blossom_car.webp=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xxxhdpi-v4\\cherry_blossom_car.webp
com.example.aimusicplayer.app-main-4\:/drawable/default_cover.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\default_cover.xml
com.example.aimusicplayer.app-main-4\:/drawable/splash_gradient.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\splash_gradient.xml
com.example.aimusicplayer.app-main-4\:/anim/slide_up.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\anim\\slide_up.xml
com.example.aimusicplayer.app-main-4\:/drawable/bg_comment_success.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\bg_comment_success.xml
com.example.aimusicplayer.app-main-4\:/mipmap-hdpi/cherry_blossom_car.webp=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-hdpi-v4\\cherry_blossom_car.webp
com.example.aimusicplayer.app-packageReleaseResources-2\:/layout/page_player_lyrics.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\page_player_lyrics.xml
com.example.aimusicplayer.app-main-4\:/drawable/search_suggestions_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\search_suggestions_background.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_next.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_next.xml
com.example.aimusicplayer.app-main-4\:/anim/rotate_play_to_pause.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\anim\\rotate_play_to_pause.xml
com.example.aimusicplayer.app-main-4\:/drawable/bg_vip_tag.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\bg_vip_tag.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_heart_mode.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_heart_mode.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_search_white.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_search_white.xml
com.example.aimusicplayer.app-main-4\:/drawable/bg_playing_cover_border.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\bg_playing_cover_border.xml
com.example.aimusicplayer.app-main-4\:/mipmap-xhdpi/ic_launcher.png=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xhdpi-v4\\ic_launcher.png
com.example.aimusicplayer.app-main-4\:/drawable/button_secondary_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\button_secondary_background.xml
com.example.aimusicplayer.app-main-4\:/mipmap-xxxhdpi/cherry_blossom_car_round.webp=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xxxhdpi-v4\\cherry_blossom_car_round.webp
com.example.aimusicplayer.app-main-4\:/drawable/ic_music_note.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_music_note.xml
com.example.aimusicplayer.app-main-4\:/anim/fragment_fade_in.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\anim\\fragment_fade_in.xml
com.example.aimusicplayer.app-main-4\:/drawable/icon_car.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\icon_car.xml
com.example.aimusicplayer.app-packageReleaseResources-2\:/layout/player_controls.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\player_controls.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_arrow_left.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_arrow_left.xml
com.example.aimusicplayer.app-main-4\:/mipmap-xxxhdpi/ic_launcher.png=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xxxhdpi-v4\\ic_launcher.png
com.example.aimusicplayer.app-main-4\:/drawable/ic_repeat_one.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_repeat_one.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_guest_login.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_guest_login.xml
com.example.aimusicplayer.app-packageReleaseResources-2\:/layout/item_online_song.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\item_online_song.xml
com.example.aimusicplayer.app-packageReleaseResources-2\:/layout/dialog_playlist.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\dialog_playlist.xml
com.example.aimusicplayer.app-main-4\:/mipmap-xhdpi/cherry_blossom_car.webp=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xhdpi-v4\\cherry_blossom_car.webp
com.example.aimusicplayer.app-main-4\:/drawable/search_results_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\search_results_background.xml
com.example.aimusicplayer.app-main-4\:/drawable/control_button_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\control_button_background.xml
com.example.aimusicplayer.app-main-4\:/drawable/button_primary.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\button_primary.xml
com.example.aimusicplayer.app-main-4\:/mipmap-hdpi/ic_launcher.png=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-hdpi-v4\\ic_launcher.png
com.example.aimusicplayer.app-main-4\:/drawable/ic_pause.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_pause.xml
com.example.aimusicplayer.app-packageReleaseResources-2\:/layout/dialog_heart_mode.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\dialog_heart_mode.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_phone_login.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_phone_login.xml
com.example.aimusicplayer.app-packageReleaseResources-2\:/layout/item_search_suggest.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\item_search_suggest.xml
com.example.aimusicplayer.app-packageReleaseResources-2\:/layout/fragment_player.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\fragment_player.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_driving.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_driving.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_play_order.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_play_order.xml
com.example.aimusicplayer.app-packageReleaseResources-2\:/layout/dialog_comment.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\dialog_comment.xml
com.example.aimusicplayer.app-main-4\:/mipmap-mdpi/cherry_blossom_car_foreground.webp=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-mdpi-v4\\cherry_blossom_car_foreground.webp
com.example.aimusicplayer.app-main-4\:/mipmap-mdpi/ic_launcher.png=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-mdpi-v4\\ic_launcher.png
com.example.aimusicplayer.app-main-4\:/drawable/round_button_background.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\round_button_background.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_random.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_random.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_playlist.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_playlist.xml
com.example.aimusicplayer.app-main-4\:/drawable/ic_playing_play_pause_selector.xml=C\:\\Users\\86158\\Desktop\\test\\Android-Voice-Controlled-Music-Player-main\\Android-Voice-Controlled-Music-Player-main\\AIMusicPlayer1\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_playing_play_pause_selector.xml
