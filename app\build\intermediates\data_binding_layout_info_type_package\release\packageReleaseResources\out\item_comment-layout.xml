<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_comment" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\item_comment.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/item_comment_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="196" endOffset="35"/></Target><Target id="@+id/image_comment_avatar" view="de.hdodenhof.circleimageview.CircleImageView"><Expressions/><location startLine="22" startOffset="8" endLine="30" endOffset="46"/></Target><Target id="@+id/text_comment_username" view="TextView"><Expressions/><location startLine="33" startOffset="8" endLine="47" endOffset="30"/></Target><Target id="@+id/text_comment_time" view="TextView"><Expressions/><location startLine="50" startOffset="8" endLine="58" endOffset="31"/></Target><Target id="@+id/text_comment_content" view="TextView"><Expressions/><location startLine="61" startOffset="8" endLine="73" endOffset="73"/></Target><Target id="@+id/layout_like" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="85" startOffset="8" endLine="119" endOffset="59"/></Target><Target id="@+id/image_like" view="ImageView"><Expressions/><location startLine="104" startOffset="16" endLine="108" endOffset="61"/></Target><Target id="@+id/text_like_count" view="TextView"><Expressions/><location startLine="110" startOffset="16" endLine="117" endOffset="38"/></Target><Target id="@+id/layout_reply" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="122" startOffset="8" endLine="156" endOffset="59"/></Target><Target id="@+id/text_reply_count" view="TextView"><Expressions/><location startLine="147" startOffset="16" endLine="154" endOffset="45"/></Target><Target id="@+id/layout_more" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="164" startOffset="8" endLine="180" endOffset="59"/></Target><Target id="@+id/recycler_view_replies" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="184" startOffset="4" endLine="194" endOffset="33"/></Target></Targets></Layout>