<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="12dp"
    android:background="?android:attr/selectableItemBackground"
    android:gravity="center_vertical">

    <!-- 专辑封面 -->
    <ImageView
        android:id="@+id/album_cover"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginEnd="12dp"
        android:scaleType="centerCrop"
        android:background="@drawable/album_art_border" />

    <!-- 歌曲信息 -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/song_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/text_light"
            android:textSize="16sp"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="4dp"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/artist_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/color_gray_300"
                android:textSize="14sp"
                android:maxLines="1"
                android:ellipsize="end" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text=" - "
                android:textColor="@color/color_gray_400"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/album_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textColor="@color/color_gray_400"
                android:textSize="14sp"
                android:maxLines="1"
                android:ellipsize="end" />

            <!-- VIP标签 -->
            <TextView
                android:id="@+id/vip_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="VIP"
                android:textColor="@color/theme_accent"
                android:textSize="12sp"
                android:textStyle="bold"
                android:background="@drawable/vip_label_background"
                android:paddingHorizontal="6dp"
                android:paddingVertical="2dp"
                android:layout_marginStart="8dp"
                android:visibility="gone" />

            <!-- 歌曲时长 -->
            <TextView
                android:id="@+id/song_duration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/color_gray_400"
                android:textSize="12sp"
                android:layout_marginStart="8dp"
                android:text="03:45" />

        </LinearLayout>

    </LinearLayout>

    <!-- 播放按钮 -->
    <ImageView
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:src="@drawable/ic_play"
        android:layout_marginStart="8dp"
        android:alpha="0.7" />

</LinearLayout>
