<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_driving_mode" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\fragment_driving_mode.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout"><Targets><Target tag="layout/fragment_driving_mode_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="243" endOffset="16"/></Target><Target id="@+id/header_container" view="LinearLayout"><Expressions/><location startLine="9" startOffset="4" endLine="32" endOffset="18"/></Target><Target id="@+id/time_text" view="TextView"><Expressions/><location startLine="16" startOffset="8" endLine="23" endOffset="38"/></Target><Target id="@+id/date_text" view="TextView"><Expressions/><location startLine="25" startOffset="8" endLine="31" endOffset="37"/></Target><Target id="@+id/album_art" view="ImageView"><Expressions/><location startLine="57" startOffset="16" endLine="62" endOffset="52"/></Target><Target id="@+id/loading_progress" view="ProgressBar"><Expressions/><location startLine="64" startOffset="16" endLine="69" endOffset="47"/></Target><Target id="@+id/song_title" view="TextView"><Expressions/><location startLine="72" startOffset="12" endLine="83" endOffset="38"/></Target><Target id="@+id/artist_text" view="TextView"><Expressions/><location startLine="85" startOffset="12" endLine="92" endOffset="52"/></Target><Target id="@+id/seek_bar" view="SeekBar"><Expressions/><location startLine="110" startOffset="16" endLine="115" endOffset="49"/></Target><Target id="@+id/current_time" view="TextView"><Expressions/><location startLine="117" startOffset="16" endLine="124" endOffset="45"/></Target><Target id="@+id/total_time" view="TextView"><Expressions/><location startLine="126" startOffset="16" endLine="134" endOffset="45"/></Target><Target id="@+id/previous_button" view="ImageButton"><Expressions/><location startLine="145" startOffset="16" endLine="154" endOffset="53"/></Target><Target id="@+id/play_pause_button" view="ImageButton"><Expressions/><location startLine="156" startOffset="16" endLine="165" endOffset="53"/></Target><Target id="@+id/next_button" view="ImageButton"><Expressions/><location startLine="167" startOffset="16" endLine="175" endOffset="44"/></Target><Target id="@+id/voice_control_container" view="LinearLayout"><Expressions/><location startLine="185" startOffset="16" endLine="211" endOffset="30"/></Target><Target id="@+id/voice_button" view="ImageButton"><Expressions/><location startLine="195" startOffset="20" endLine="203" endOffset="60"/></Target><Target id="@+id/exit_container" view="LinearLayout"><Expressions/><location startLine="213" startOffset="16" endLine="239" endOffset="30"/></Target><Target id="@+id/exit_button" view="ImageButton"><Expressions/><location startLine="223" startOffset="20" endLine="231" endOffset="60"/></Target></Targets></Layout>