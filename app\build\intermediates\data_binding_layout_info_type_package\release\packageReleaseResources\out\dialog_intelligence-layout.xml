<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_intelligence" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\dialog_intelligence.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_intelligence_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="74" endOffset="14"/></Target><Target id="@+id/text_intelligence_title" view="TextView"><Expressions/><location startLine="16" startOffset="8" endLine="24" endOffset="35"/></Target><Target id="@+id/btn_close_intelligence" view="ImageButton"><Expressions/><location startLine="26" startOffset="8" endLine="34" endOffset="46"/></Target><Target id="@+id/loading_view_intelligence" view="com.example.aimusicplayer.ui.widget.LottieLoadingView"><Expressions/><location startLine="38" startOffset="4" endLine="48" endOffset="25"/></Target><Target id="@+id/text_empty_intelligence" view="TextView"><Expressions/><location startLine="51" startOffset="4" endLine="61" endOffset="33"/></Target><Target id="@+id/recycler_view_intelligence" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="64" startOffset="4" endLine="72" endOffset="53"/></Target></Targets></Layout>