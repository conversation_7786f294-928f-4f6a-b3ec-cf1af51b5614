<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_play_queue" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\dialog_play_queue.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_play_queue_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="183" endOffset="14"/></Target><Target id="@+id/tv_song_count" view="TextView"><Expressions/><location startLine="30" startOffset="8" endLine="37" endOffset="32"/></Target><Target id="@+id/iv_close" view="ImageView"><Expressions/><location startLine="40" startOffset="8" endLine="47" endOffset="36"/></Target><Target id="@+id/ll_play_mode" view="LinearLayout"><Expressions/><location startLine="62" startOffset="8" endLine="89" endOffset="22"/></Target><Target id="@+id/iv_play_mode" view="ImageView"><Expressions/><location startLine="73" startOffset="12" endLine="78" endOffset="40"/></Target><Target id="@+id/tv_play_mode" view="TextView"><Expressions/><location startLine="80" startOffset="12" endLine="87" endOffset="41"/></Target><Target id="@+id/btn_shuffle" view="TextView"><Expressions/><location startLine="92" startOffset="8" endLine="107" endOffset="37"/></Target><Target id="@+id/btn_clear" view="TextView"><Expressions/><location startLine="110" startOffset="8" endLine="125" endOffset="37"/></Target><Target id="@+id/rv_play_queue" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="136" startOffset="8" endLine="144" endOffset="54"/></Target><Target id="@+id/ll_empty_state" view="LinearLayout"><Expressions/><location startLine="147" startOffset="8" endLine="179" endOffset="22"/></Target></Targets></Layout>