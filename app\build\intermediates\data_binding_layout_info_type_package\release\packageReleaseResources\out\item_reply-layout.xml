<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_reply" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\item_reply.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/item_reply_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="116" endOffset="51"/></Target><Target id="@+id/image_avatar" view="de.hdodenhof.circleimageview.CircleImageView"><Expressions/><location startLine="12" startOffset="4" endLine="21" endOffset="46"/></Target><Target id="@+id/text_username" view="TextView"><Expressions/><location startLine="24" startOffset="4" endLine="38" endOffset="26"/></Target><Target id="@+id/text_reply_time" view="TextView"><Expressions/><location startLine="41" startOffset="4" endLine="49" endOffset="27"/></Target><Target id="@+id/text_reply_content" view="TextView"><Expressions/><location startLine="52" startOffset="4" endLine="63" endOffset="71"/></Target><Target id="@+id/layout_reply_like" view="LinearLayout"><Expressions/><location startLine="75" startOffset="8" endLine="100" endOffset="22"/></Target><Target id="@+id/image_reply_like" view="ImageView"><Expressions/><location startLine="86" startOffset="12" endLine="90" endOffset="57"/></Target><Target id="@+id/text_reply_like_count" view="TextView"><Expressions/><location startLine="92" startOffset="12" endLine="99" endOffset="34"/></Target><Target id="@+id/text_reply_to_reply" view="TextView"><Expressions/><location startLine="103" startOffset="8" endLine="114" endOffset="38"/></Target></Targets></Layout>