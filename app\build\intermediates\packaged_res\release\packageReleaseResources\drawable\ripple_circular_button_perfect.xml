<?xml version="1.0" encoding="utf-8"?>
<ripple xmlns:android="http://schemas.android.com/apk/res/android"
    android:color="@color/theme_accent_light">
    <item>
        <layer-list>
            <!-- 透明背景，确保按钮区域可点击 -->
            <item>
                <shape android:shape="rectangle">
                    <solid android:color="@android:color/transparent" />
                </shape>
            </item>
            <!-- 居中的正圆形蓝色背景 -->
            <item android:gravity="center">
                <shape android:shape="oval">
                    <solid android:color="@color/theme_accent" />
                    <size
                        android:width="40dp"
                        android:height="40dp" />
                </shape>
            </item>
        </layer-list>
    </item>
</ripple>
