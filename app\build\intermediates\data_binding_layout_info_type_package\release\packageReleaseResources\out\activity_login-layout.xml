<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_login" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\activity_login.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout" rootNodeViewId="@+id/login_root_layout"><Targets><Target id="@+id/login_root_layout" tag="layout/activity_login_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="246" endOffset="16"/></Target><Target id="@+id/app_logo" view="ImageView"><Expressions/><location startLine="36" startOffset="12" endLine="42" endOffset="52"/></Target><Target id="@+id/tv_welcome" view="TextView"><Expressions/><location startLine="45" startOffset="12" endLine="60" endOffset="42"/></Target><Target id="@+id/tv_subtitle" view="TextView"><Expressions/><location startLine="63" startOffset="12" endLine="73" endOffset="41"/></Target><Target id="@+id/login_buttons_container" view="LinearLayout"><Expressions/><location startLine="76" startOffset="12" endLine="200" endOffset="26"/></Target><Target id="@+id/btn_qrcode_login" view="LinearLayout"><Expressions/><location startLine="86" startOffset="16" endLine="122" endOffset="30"/></Target><Target id="@+id/btn_phone_login" view="LinearLayout"><Expressions/><location startLine="125" startOffset="16" endLine="161" endOffset="30"/></Target><Target id="@+id/btn_guest_login" view="LinearLayout"><Expressions/><location startLine="164" startOffset="16" endLine="199" endOffset="30"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="225" startOffset="8" endLine="229" endOffset="39"/></Target><Target id="@+id/tv_status" view="TextView"><Expressions/><location startLine="231" startOffset="8" endLine="243" endOffset="39"/></Target></Targets></Layout>