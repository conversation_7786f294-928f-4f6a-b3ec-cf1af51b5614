<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_song" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\item_song.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/item_song_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="79" endOffset="51"/></Target><Target id="@+id/image_song_cover" view="ImageView"><Expressions/><location startLine="9" startOffset="4" endLine="18" endOffset="49"/></Target><Target id="@+id/text_song_title" view="TextView"><Expressions/><location startLine="20" startOffset="4" endLine="33" endOffset="27"/></Target><Target id="@+id/text_song_artist" view="TextView"><Expressions/><location startLine="35" startOffset="4" endLine="48" endOffset="27"/></Target><Target id="@+id/text_vip_tag" view="TextView"><Expressions/><location startLine="50" startOffset="4" endLine="66" endOffset="36"/></Target><Target id="@+id/text_song_duration" view="TextView"><Expressions/><location startLine="68" startOffset="4" endLine="77" endOffset="28"/></Target></Targets></Layout>