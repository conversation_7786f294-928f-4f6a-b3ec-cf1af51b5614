<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_qr_login" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\dialog_qr_login.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout"><Targets><Target tag="layout/dialog_qr_login_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="118" endOffset="16"/></Target><Target id="@+id/tv_title" view="TextView"><Expressions/><location startLine="7" startOffset="4" endLine="14" endOffset="33"/></Target><Target id="@+id/tv_subtitle" view="TextView"><Expressions/><location startLine="16" startOffset="4" endLine="24" endOffset="32"/></Target><Target id="@+id/qr_container" view="FrameLayout"><Expressions/><location startLine="26" startOffset="4" endLine="95" endOffset="17"/></Target><Target id="@+id/qr_image" view="ImageView"><Expressions/><location startLine="36" startOffset="8" endLine="40" endOffset="42"/></Target><Target id="@+id/qr_loading" view="ProgressBar"><Expressions/><location startLine="42" startOffset="8" endLine="47" endOffset="61"/></Target><Target id="@+id/qr_error_container" view="LinearLayout"><Expressions/><location startLine="49" startOffset="8" endLine="84" endOffset="22"/></Target><Target id="@+id/qr_error_icon" view="ImageView"><Expressions/><location startLine="58" startOffset="12" endLine="63" endOffset="60"/></Target><Target id="@+id/qr_error_text" view="TextView"><Expressions/><location startLine="65" startOffset="12" endLine="72" endOffset="65"/></Target><Target id="@+id/btn_reload_qr" view="Button"><Expressions/><location startLine="74" startOffset="12" endLine="83" endOffset="54"/></Target><Target id="@+id/qr_status" view="TextView"><Expressions/><location startLine="86" startOffset="8" endLine="94" endOffset="38"/></Target><Target id="@+id/tv_tip" view="TextView"><Expressions/><location startLine="97" startOffset="4" endLine="106" endOffset="32"/></Target><Target id="@+id/btn_cancel" view="Button"><Expressions/><location startLine="108" startOffset="4" endLine="116" endOffset="55"/></Target></Targets></Layout>